# Debug Guide - Notification Issues

## Vấn đề đã sửa:

### 1. **Foreground Notification**
- **Trước**: Log "Foreground message received" nhưng không hiện thông báo
- **Sau**: Vẫn log nhưng không hiện thông báo (đ<PERSON><PERSON> là đúng)
- **Lý do**: Khi user đang dùng app, không cần hiện notification popup

### 2. **Background/Killed Duplicate Notifications**
- **Trước**: Nhận 2 thông báo - 1 tap không hoạt động, 1 tap hoạt động
- **Sau**: Chỉ 1 thông báo với navigation hoạt động đúng
- **Cải thiện**:
  - Better duplicate prevention với timeout 5s
  - Unique messageId với timestamp
  - Proper data handling cho navigation

## Logs để debug:

### Foreground (App đang dùng):
```
Foreground message received: {notification: {...}, data: {...}}
```
- ✅ Không hiển thị notification popup
- ✅ User tiếp tục dùng app bình thường

### Background (App chạy nền):
```
Background message received: {notification: {...}, data: {...}}
Displaying notification: messageId_timestamp
```
- ✅ Hiển thị 1 notification duy nhất
- Khi tap:
```
Notification opened app from background state: {...}
Handling FCM notification tap: {...}
Navigating to: NOTIFICATION {...}
```

### App Killed:
```
Background message received: {notification: {...}, data: {...}}
Displaying notification: messageId_timestamp
```
- ✅ Hiển thị notification
- Khi tap (app khởi động):
```
App launched from notification: {...}
Handling FCM notification tap: {...}
Navigating to: NOTIFICATION {...}
```

### User Logout:
```
Handling FCM notification tap: {...}
User not logged in, saving pending navigation: {screen: "NOTIFICATION", params: {...}}
```
- Sau khi login:
```
Processing pending navigation: {screen: "NOTIFICATION", params: {...}}
Navigating to: NOTIFICATION {...}
```

## Test Cases:

### Test 1: Foreground
1. Mở app và đang sử dụng
2. Gửi push notification
3. **Kỳ vọng**: Log "Foreground message received", không popup notification

### Test 2: Background
1. Minimize app (không force close)
2. Gửi push notification
3. **Kỳ vọng**: Hiện 1 notification trong system tray
4. Tap notification
5. **Kỳ vọng**: App mở và navigate đến NOTIFICATION screen

### Test 3: App Killed
1. Force close app hoàn toàn
2. Gửi push notification
3. **Kỳ vọng**: Hiện notification trong system tray
4. Tap notification
5. **Kỳ vọng**: App khởi động và navigate đến NOTIFICATION screen

### Test 4: Duplicate Prevention
1. Gửi cùng 1 notification liên tiếp (trong 5s)
2. **Kỳ vọng**: Chỉ hiện 1 notification

### Test 5: User Logout
1. Logout khỏi app
2. Gửi push notification và tap
3. **Kỳ vọng**: Lưu pending navigation
4. Login lại
5. **Kỳ vọng**: Tự động navigate đến NOTIFICATION screen

## Cách gửi test notification:

### Firebase Console:
```
Title: Test Notification
Body: Testing notification flow
Target: Your app
Custom data:
- target: NOTIFICATION
```

### Postman/curl:
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "DEVICE_TOKEN",
    "notification": {
      "title": "Test Notification",
      "body": "Testing notification flow"
    },
    "data": {
      "target": "NOTIFICATION"
    }
  }'
```

## Troubleshooting:

### Nếu vẫn nhận duplicate:
- Check logs xem có 2 "Displaying notification" không
- Kiểm tra messageId có unique không
- Verify timeout 5s hoạt động

### Nếu tap không navigate:
- Check logs "Handling FCM notification tap"
- Verify user login state
- Check pending navigation logs

### Nếu app crash:
- Check navigation service import
- Verify screen name APP_SCREEN.NOTIFICATION exists
- Check store state structure
