# Test Cases cho Luồng Thông Báo Mới

## Các trường hợp cần test:

### 1. App ở Foreground
- **Mô tả**: User đang sử dụng app, nhận notification
- **Kỳ vọng**: 
  - Không hiển thị notification popup (vì user đang dùng app)
  - Notification data vẫn được xử lý trong app
- **Test**: Gửi push notification khi app đang mở

### 2. App ở Background
- **Mô tả**: App đang chạy background, user nhận notification
- **Kỳ vọng**:
  - Hiển thị notification trong system tray
  - Khi tap notification, app mở và navigate đến NOTIFICATION screen
- **Test**: Minimize app, gửi push notification, tap notification

### 3. App bị Kill/Closed
- **Mô tả**: App hoàn toàn đóng, user nhận notification
- **<PERSON><PERSON> vọng**:
  - Hiển thị notification trong system tray
  - Khi tap notification, app khởi động và navigate đến NOTIFICATION screen
- **Test**: Force close app, gửi push notification, tap notification

### 4. User đang Logout
- **<PERSON>ô tả**: User chưa đăng nhập, nhận notification và tap vào
- **Kỳ vọng**:
  - Lưu pending navigation
  - Sau khi login thành công, tự động navigate đến NOTIFICATION screen
- **Test**: Logout, gửi push notification, tap notification, login

### 5. Duplicate Notification Prevention
- **Mô tả**: Cùng 1 notification được gửi nhiều lần
- **Kỳ vọng**:
  - Chỉ hiển thị 1 notification duy nhất
- **Test**: Gửi cùng 1 notification liên tiếp

### 6. App State Transition
- **Mô tả**: App chuyển từ background về foreground
- **Kỳ vọng**:
  - Kiểm tra pending navigation và thực hiện nếu có
- **Test**: Có pending navigation, chuyển app từ background về foreground

## Cách test:

### Sử dụng Firebase Console:
1. Vào Firebase Console > Cloud Messaging
2. Tạo notification mới với:
   - Title: "Test Notification"
   - Body: "Testing notification flow"
   - Target: App của bạn
   - Additional options > Custom data:
     - Key: "target", Value: "NOTIFICATION"

### Sử dụng Postman/curl:
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "DEVICE_TOKEN",
    "notification": {
      "title": "Test Notification",
      "body": "Testing notification flow"
    },
    "data": {
      "target": "NOTIFICATION"
    }
  }'
```

## Debug logs để kiểm tra:

1. **NotificationService logs**:
   - "Foreground message received:"
   - "Notification opened app from background state:"
   - "User pressed notification in foreground:"
   - "User not logged in, saving pending navigation:"
   - "Processing pending navigation:"
   - "Navigating to:"

2. **Background handler logs**:
   - "Background message received:"
   - "Background Notifee event:"

3. **App state logs**:
   - "App has come to the foreground!"

## Checklist:

- [ ] Test foreground notification (không hiển thị popup)
- [ ] Test background notification (hiển thị và navigate)
- [ ] Test app closed notification (khởi động và navigate)
- [ ] Test logout scenario (pending navigation)
- [ ] Test duplicate prevention
- [ ] Test app state transition
- [ ] Verify navigation đến đúng NOTIFICATION screen
- [ ] Verify không có crash hoặc error
