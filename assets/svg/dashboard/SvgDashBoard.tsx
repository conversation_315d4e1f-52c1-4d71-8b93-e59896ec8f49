import * as React from 'react';
import Svg, {Clip<PERSON><PERSON>, Defs, G, Image, Path} from 'react-native-svg';
import {Dimensions} from 'react-native';

const {width, height} = Dimensions.get('screen');

interface actionProps {
  onPressMyClass: () => void;
  onPressMission: () => void;
  onPressPractice: () => void;
  onPressLeaderBoard: () => void;
  onPressFunTalk: () => void;
}

const SvgDashboard: React.FC<actionProps> = ({
  onPressPractice,
  onPressMission,
  onPressLeaderBoard,
  onPressFunTalk,
  onPressMyClass,
}) => {
  return (
    <Svg
      width={width}
      height={height}
      preserveAspectRatio="xMidYMid slice"
      viewBox="0 0 1125 2436"
      fill="none">
      <G id="Dashboard Background Final 1" clipPath="url(#clip0_444_67)">
        <Image
          x={0}
          y={0}
          width={1126}
          height={2437}
          href={require('./bgMain.png')}
          preserveAspectRatio="none"
        />

        <G id="mySchool" onPressIn={onPressMyClass}>
          <Image
            x={329.05}
            y={493.762}
            width={793.44}
            height={438.72}
            href={require('./mySchool.png')}
            preserveAspectRatio="none"
          />
        </G>
        <G id="mission" onPressIn={onPressMission}>
          <Image
            x={511.69}
            y={1028.04}
            width={620.34}
            height={430.32}
            href={require('./mission.png')}
            preserveAspectRatio="none"
          />
        </G>
        <G id="practice" onPressIn={onPressPractice}>
          <Image
            x={20.55}
            y={1390.49}
            width={560.16}
            height={441.6}
            href={require('./practice.png')}
            preserveAspectRatio="none"
          />
        </G>
        <G id="leaderBoard" onPressIn={onPressLeaderBoard}>
          <Image
            x={67.69}
            y={750.922}
            width={330.96}
            height={463.44}
            href={require('./leaderBoard.png')}
            preserveAspectRatio="none"
          />
        </G>
        <G id="funtalk" onPressIn={onPressFunTalk}>
          <Image
            x={247.09}
            y={1756.52}
            width={776.64}
            height={502.8}
            href={require('./funtalk.png')}
            preserveAspectRatio="none"
          />
        </G>
      </G>
      <Defs>
        <ClipPath id="clip0_444_67">
          <Path fill="#fff" d="M0 0h1125v2436H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default SvgDashboard;
