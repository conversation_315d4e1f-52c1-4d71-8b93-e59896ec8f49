import {createSlice} from '@reduxjs/toolkit';
import {
  fetchAuth,
  fetchForgotPassword,
  fetchRegister,
  fetchResetPassword,
  fetchVerifyOTP,
} from './fetchData.ts';
import NotificationService from '../../../common/firebase/NotificationService';

interface AuthSliceProps {
  error: string;
  loading: boolean;
  token: string;
  deviceToken: string;
  remember?: {
    username: string;
    password: string;
  };
  firstOpen?: boolean;
}

const initialState: AuthSliceProps = {
  error: '',
  loading: false,
  token: '',
  deviceToken: '',
  remember: undefined,
  firstOpen: true,
};

const AuthSlice = createSlice({
  name: 'Auth',
  initialState,
  reducers: {
    resetData: () => initialState,
    logIn: (state, action) => {
      state.loading = false;
      state.token = action.payload;
      state.firstOpen = false;
    },
    logOut: state => {
      state.token = '';
    },
    rememberPass: (state, action) => {
      state.remember = action.payload;
    },
    saveDeviceToken: (state, action) => {
      state.deviceToken = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchAuth.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchAuth.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAuth.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.access_token;
        state.firstOpen = false;
      })
      .addCase(fetchRegister.pending, state => {
        state.loading = true;
      })
      .addCase(fetchRegister.rejected, state => {
        state.loading = false;
      })
      .addCase(fetchRegister.fulfilled, state => {
        state.loading = false;
      })
      .addCase(fetchForgotPassword.pending, state => {
        state.loading = true;
      })
      .addCase(fetchForgotPassword.rejected, state => {
        state.loading = false;
      })
      .addCase(fetchForgotPassword.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(fetchVerifyOTP.pending, state => {
        state.loading = true;
      })
      .addCase(fetchVerifyOTP.rejected, state => {
        state.loading = false;
      })
      .addCase(fetchVerifyOTP.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(fetchResetPassword.pending, state => {
        state.loading = true;
      })
      .addCase(fetchResetPassword.rejected, state => {
        state.loading = false;
      })
      .addCase(fetchResetPassword.fulfilled, (state, action) => {
        state.loading = false;
      });
  },
});
export const {resetData, logOut, logIn, rememberPass, saveDeviceToken} =
  AuthSlice.actions;
export default AuthSlice.reducer;
