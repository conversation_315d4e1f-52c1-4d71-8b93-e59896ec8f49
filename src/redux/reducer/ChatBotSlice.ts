import {createSlice} from '@reduxjs/toolkit';
import {fetchChatbot, fetchChatbotPhonemes} from './fetchData';

interface ChatBotSliceData {
  dataChatBot?: ChatBotResponse;
  loading: boolean;
  error: string;
  dataLocal: any[];
  dataPhonemes?: ChatBotResponse;
}

const initialState: ChatBotSliceData = {
  dataChatBot: undefined,
  loading: false,
  error: '',
  dataLocal: [],
  dataPhonemes: undefined,
};

// Utility: format message
const formatMessage = (data: any, sender: 'user' | 'bot') => ({
  ...data,
  sender,
  createdAt: new Date().toISOString(),
});

const ChatBotSlice = createSlice({
  name: 'chatbot',
  initialState,
  reducers: {
    resetDataChatbot: () => initialState,
    addDataLocal: (state, action) => {
      state.dataLocal.push(action.payload);
    },
  },
  extraReducers: builder => {
    const handlePending = (state: ChatBotSliceData) => {
      state.loading = true;
      state.error = '';
    };

    const handleRejected = (state: ChatBotSliceData, action: any) => {
      state.loading = false;
      state.error = action.payload as string;
    };

    builder
      .addCase(fetchChatbotPhonemes.pending, handlePending)
      .addCase(fetchChatbotPhonemes.fulfilled, (state, action) => {
        state.loading = false;
        state.dataPhonemes = action.payload.data;
        state.dataLocal.push(formatMessage(action.payload.data, 'user'));
      })
      .addCase(fetchChatbotPhonemes.rejected, handleRejected)

      .addCase(fetchChatbot.pending, handlePending)
      .addCase(fetchChatbot.fulfilled, (state, action) => {
        state.loading = false;
        state.dataChatBot = action.payload.data;
        state.dataLocal.push(formatMessage(action.payload.data, 'bot'));
      })
      .addCase(fetchChatbot.rejected, handleRejected);
  },
});

export const {resetDataChatbot, addDataLocal} = ChatBotSlice.actions;
export default ChatBotSlice.reducer;
