import {createSlice} from '@reduxjs/toolkit';
import {fetchCountMessage, fetchDataInfo, fetchReadMessage} from './fetchData';

interface ProfileSliceData {
  data?: ProfileData;
  loading: boolean;
  error: string;
  countNotification: number;
}

const initialState: ProfileSliceData = {
  data: undefined,
  loading: false,
  error: '',
  countNotification: 0,
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {resetData: () => initialState},
  extraReducers: builder => {
    builder
      .addCase(fetchDataInfo.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchDataInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action?.payload;
      })
      .addCase(fetchDataInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchCountMessage.fulfilled, (state, action) => {
        state.countNotification = action.payload?.data;
      })
      .addCase(fetchReadMessage.fulfilled, state => {
        state.countNotification = state.countNotification - 1;
      });
  },
});

export default profileSlice.reducer;
