import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator, TransitionPresets} from '@react-navigation/stack';
import React, {useEffect} from 'react';
import Toast, {ToastConfig} from 'react-native-toast-message';
import {toastConfig} from '../components/Toast.tsx';
import {useNetInfo} from '../hooks/useNetInfo.ts';
import {useReduxDispatch, useTypedSelector} from '../redux/store.ts';
import {isIOS} from '../utils/Scale.ts';
import AppNavigator from './AppNavigator.tsx';
import AuthNavigator from './AuthNavigator';
import {navigationRef} from './NavigationServices.ts';
import {APP_SCREEN, RootStackParamList} from './screenType.ts';
import {
  getDeviceToken,
  requestNotificationPermission,
} from '../../common/firebase/firebaseMessage.ts';
import {saveDeviceToken} from '../redux/reducer/AuthSlice.ts';
import RNBootSplash from 'react-native-bootsplash';
import {OffLineBar} from '../components/OfflineBar.tsx';
import messaging from '@react-native-firebase/messaging';
import notifee from '@notifee/react-native';

const RootStack = createStackNavigator<RootStackParamList>();

const Navigation = () => {
  useNetInfo();
  const {data} = useTypedSelector(state => state.profile);
  const {token} = useTypedSelector(state => state.auth);
  const dispatch = useReduxDispatch();
  let initialRoute;

  useEffect(() => {
    const initNotifications = async () => {
      await requestNotificationPermission();
      const token = await getDeviceToken();
      dispatch(saveDeviceToken(token));
    };
    initNotifications();
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('Foreground message:', remoteMessage);
      await notifee.displayNotification({
        title: remoteMessage?.notification?.title,
        body: remoteMessage?.notification?.body,
        android: {
          channelId: 'default',
          pressAction: {
            id: 'open_notification',
          },
        },
        data: {
          target: APP_SCREEN.NOTIFICATION,
        },
      });
    });
    return unsubscribe();
  }, []);

  if (
    !data ||
    !data?.authorities ||
    data?.authorities?.length === 0 ||
    !token
  ) {
    initialRoute = APP_SCREEN.AUTH;
  } else {
    initialRoute = APP_SCREEN.UN_AUTH;
  }

  return (
    <NavigationContainer
      ref={navigationRef}
      onReady={() => RNBootSplash.hide({fade: true})}>
      <RootStack.Navigator
        screenOptions={{
          ...TransitionPresets.SlideFromRightIOS,
          headerShown: false,
          gestureEnabled: true,
        }}
        initialRouteName={initialRoute}>
        <RootStack.Screen name={APP_SCREEN.UN_AUTH} component={AppNavigator} />
        <RootStack.Screen name={APP_SCREEN.AUTH} component={AuthNavigator} />
      </RootStack.Navigator>
      <OffLineBar />
      <Toast
        config={toastConfig as ToastConfig}
        position="bottom"
        bottomOffset={isIOS ? 50 : 20}
      />
    </NavigationContainer>
  );
};

export default Navigation;
