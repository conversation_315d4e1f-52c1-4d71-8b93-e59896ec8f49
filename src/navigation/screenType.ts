import {NativeStackScreenProps as RNStackScreenProps} from '@react-navigation/native-stack';

export enum APP_SCREEN {
  AUTH = 'AUTH',
  UN_AUTH = 'UN_AUTH',
  CHAT_BOT = 'CHAT_BOT_SCREEN',
  LOGIN = '<PERSON><PERSON><PERSON><PERSON>_SCREEN',
  SPLASH = '<PERSON><PERSON><PERSON>H_SCREEN',
  REGISTER = 'REGISTER_SCREEN',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD_SCREEN',
  CHOOSE_CHARACTER = 'CHOOSE_CHARACTER',
  QUESTIONS = 'QUESTIONS_SCREEN',
  LESSON = 'LESSON_SCREEN',
  WELLCOME = 'WELLCOME',
  HOME = 'HOME',
  PROFILE = 'PROFILE',
  UNIT = 'UNIT',
  MY_SCHOOL = 'MY_SCHOOL',
  LEADERBOARD = 'LEADERBOARD',
  PRACTICE = 'PRACTICE',
  YOUR_CLASS = 'YOUR_CLASS',
  NOTIFICATION = 'NOTIFICATION',
  CLASS_LEADERBOARD = 'CLASS_LEADERBOARD',
}

export enum BOTTOM_TAB_ROUTE_NAME {
  HOME = 'HOME',
  CHAT_BOT = 'CHAT_BOT',
  PROFILE = 'PROFILE',
}

export type RootStackParamList = {
  [APP_SCREEN.AUTH]: undefined;
  [APP_SCREEN.UN_AUTH]: undefined;
  [APP_SCREEN.CHAT_BOT]: undefined;
  [APP_SCREEN.LOGIN]: undefined;
  [APP_SCREEN.REGISTER]: undefined;
  [APP_SCREEN.FORGOT_PASSWORD]: undefined;
  [APP_SCREEN.CHOOSE_CHARACTER]: undefined;
  [APP_SCREEN.SPLASH]: undefined;
  [APP_SCREEN.QUESTIONS]: {item: Lesson; isDone: boolean};
  [APP_SCREEN.LESSON]: {unitId: string};
  [APP_SCREEN.WELLCOME]: undefined;
  [APP_SCREEN.HOME]: undefined;
  [APP_SCREEN.PROFILE]: undefined;
  [APP_SCREEN.UNIT]: {classId?: string};
  [APP_SCREEN.LEADERBOARD]: undefined;
  [APP_SCREEN.PRACTICE]: undefined;
  [APP_SCREEN.MY_SCHOOL]: undefined;
  [APP_SCREEN.YOUR_CLASS]: {classId: string; classStatus: number};
  [APP_SCREEN.NOTIFICATION]: undefined;
  [APP_SCREEN.CLASS_LEADERBOARD]: {classId: string};
};

export type StackScreenProps<T extends keyof RootStackParamList> =
  RNStackScreenProps<RootStackParamList, T>;
