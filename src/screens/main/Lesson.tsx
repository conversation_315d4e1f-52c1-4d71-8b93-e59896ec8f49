import React, {useCallback, useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  InteractionManager,
  ListRenderItem,
  StyleSheet,
  View,
} from 'react-native';
import Header from '../../components/Header';
import ItemQuest1 from '../../../assets/svg/quest/ItemQuest1';
import ItemQuest2 from '../../../assets/svg/quest/ItemQuest2';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {fetchLessonWithUnitID} from '../../redux/reducer/fetchData';
import {LoadResource} from '../../components/LoadResource';
import {resetDataQuestion} from '../../redux/reducer/QuestionSlice.ts';
import {useIsFocused} from '@react-navigation/native';
import LessonModal from '../../components/modal/LessonModal.tsx';
import {goBack, navigate} from '../../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../../navigation/screenType.ts';
import useSound from '../../hooks/useSound.ts';

type Lesson = {
  id: string;
};

type ChunkedGroup = {
  type: 'ItemQuest1' | 'ItemQuest2';
  data: Lesson[];
};

const groupLessons = (data: Lesson[]): ChunkedGroup[] => {
  const groups: ChunkedGroup[] = [];
  if (!data) {
    return groups;
  }
  if (data?.length <= 8) {
    groups.push({
      type: 'ItemQuest1',
      data,
    });
  } else {
    groups.push({
      type: 'ItemQuest1',
      data: data?.slice(0, 8),
    });

    let currentIndex = 8;
    while (currentIndex < data?.length) {
      const chunk = data?.slice(currentIndex, currentIndex + 8);
      groups.push({
        type: 'ItemQuest2',
        data: chunk,
      });
      currentIndex += 8;
    }
  }

  return groups;
};

function getShuffledFileNames() {
  const fileNames = [
    'the_bee_is_in_the_tree',
    'my_mate_is_at_the_gate',
    'lets_bake_a_cake',
    'a_snake_sneaks_to_seek_a_snack',
    'she_sells_seashells_by_the_seashore',
    'sheep_on_the_ship',
  ];

  const randomIndex = Math.floor(Math.random() * fileNames.length);
  return fileNames[randomIndex];
}

const LessonScreen = ({route}: any) => {
  const {unitId} = route?.params;

  const dispatch = useReduxDispatch();
  const {data, loading, dataAnswer} = useTypedSelector(state => state.lesson);
  const [page, setPage] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [itemData, setItemdata] = useState<any>(null);
  const [ready, setReady] = useState<boolean>(false);
  const isFocus = useIsFocused();
  const [loadUI, setLoadUI] = useState(false);
  const [keySound, setkeysound] = useState<string>('');
  const {playLocalFile} = useSound();
  const sound = getShuffledFileNames();
  useEffect(() => {
    const task = InteractionManager.runAfterInteractions(() => {
      setReady(true);
    });

    return () => task.cancel();
  }, []);
  useEffect(() => {
    playLocalFile(sound, false);
    setkeysound(sound);
    const timer = setTimeout(() => {
      setLoadUI(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);
  useEffect(() => {
    dispatch(fetchLessonWithUnitID(unitId));
  }, [dispatch, unitId, isFocus]);

  useEffect(() => {
    dispatch(resetDataQuestion());
  }, [isFocus]);

  const handleGoToQuestion = useCallback((item: Lesson) => {
    setItemdata(item);
    setVisible(true);
  }, []);
  const onRedo = () => {
    navigate(APP_SCREEN.QUESTIONS, {item: itemData, isDone: false});
    setVisible(false);
  };
  const onStart = () => {
    navigate(APP_SCREEN.QUESTIONS, {item: itemData, isDone: false});

    setVisible(false);
  };
  const onPreView = () => {
    navigate(APP_SCREEN.QUESTIONS, {item: itemData, isDone: true});
    setVisible(false);
  };
  // const loadMore = () => {
  //   if (isLoadingMore) return;
  //   setIsLoadingMore(true);
  //   dispatch(fetchLessonWithUnitID(unitId, page + 1)).finally(() => {
  //     setIsLoadingMore(false);
  //     setPage(prev => prev + 1);
  //   });
  // };

  const chunks = groupLessons(data);

  const renderItem: ListRenderItem<ChunkedGroup> = ({item, index}) => {
    if (item.type === 'ItemQuest1') {
      return (
        <ItemQuest1
          key={`ItemQuest1-${index}`}
          data={item.data}
          active={1}
          onItemPress={(item, idx) => handleGoToQuestion(item)}
        />
      );
    }
    return (
      <ItemQuest2
        key={`ItemQuest2-${index}`}
        data={item.data}
        active={1}
        onItemPress={(item, idx) => handleGoToQuestion(item)}
      />
    );
  };
  return (
    <View style={styles.container}>
      <Header
        title=""
        onBackPress={() => (visible ? setVisible(false) : goBack())}
      />
      {!loadUI || loading || !ready ? (
        <LoadResource text={keySound} />
      ) : (
        <FlatList
          data={chunks}
          renderItem={renderItem}
          keyExtractor={(_, index) => `chunk-${index}`}
          // onEndReached={loadMore}
          onEndReachedThreshold={0.8}
          ListFooterComponent={isLoadingMore ? <ActivityIndicator /> : null}
        />
      )}
      <LessonModal
        isVisible={visible}
        onClose={() => setVisible(false)}
        title={itemData?.name || ''}
        numberQuestion={itemData?.numOfExcercises || ''}
        time={itemData?.duration || ''}
        isDone={itemData?.isDone}
        numberOfAttempt={itemData?.numberOfAttempt}
        numberOfDaysLeft={itemData?.numberOfDaysLeft}
        isClock={
          (itemData?.statusAssign == 1 || itemData?.statusAssign == 2) &&
          itemData?.numberOfAttempt > 0
            ? false
            : (itemData?.statusAssign === 1 &&
                  (itemData?.attemptLimit == null ||
                    itemData?.numberOfAttempt < itemData?.attemptLimit)) ||
                (itemData?.statusAssign === 2 && itemData?.attemptLimit == null)
              ? false
              : true
        }
        onRedo={onRedo}
        onReview={onPreView}
        onStart={onStart}
      />
    </View>
  );
};

export default LessonScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
