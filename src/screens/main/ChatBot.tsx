import React, {useState} from 'react';
import {
  Image,
  ImageBackground,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {Theme} from '../../themes';
import Header from '../../components/Header.tsx';
import CustomModal from '../../components/CustomModal.tsx';
import Button from '../../components/Button.tsx';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg/index.tsx';
import {goBack} from '../../navigation/NavigationServices.ts';

const modalData = [
  {
    title: 'Favorite food',
    image: Theme.images.favoriteFood,
    question: 'What do you like to eat?',
    mission: `🎯 Mission: Tell Cheppy your 3 \nfavorite foods and ask him!`,
    reward: "You'll get 20",
  },
  {
    title: 'Favorite color',
    image: Theme.images.yourPet,
    question: 'What is your favorite color?',
    mission: '🎯 Mission: Share your top 3 \nfavorite colors with Cheppy!',
    reward: "You'll get 15",
  },
  {
    title: 'Your pet',
    image: Theme.images.yourPet,
    question: 'Let’s talk about pets!',
    mission:
      '🎯 Mission: Tell Cheppy what pet you \nhave or want and what it can do.',
    reward: "You'll get 25",
  },
  {
    title: 'Favorite place',
    image: Theme.images.yourPet,
    question: 'Where do you like to go?',
    mission: '🎯 Mission: Share your top 3 \nfavorite places with Cheppy!',
    reward: "You'll get 30",
  },
];

const ChatBot: React.FC = () => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalContent, setModalContent] = useState<{
    title: string;
    image: any;
    question: string;
    mission: string;
    reward: string;
  } | null>(null);
  const [showMission, setShowMission] = useState<boolean>(false);
  const [quitModal, setQuitModal] = useState<boolean>(false);
  const [finishModal, setFinishModal] = useState<boolean>(false);

  const handleChooseCard = (index: number) => {
    setModalContent(modalData[index]);
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
    setTimeout(() => {
      setModalContent(null);
    }, 300);
  };

  const closeQuitModal = () => {
    setQuitModal(false);
  };

  const handleQuit = () => {
    setQuitModal(false);
    setTimeout(() => {
      goBack();
    }, 300);
  };

  const handleStartNow = () => {
    handleCloseModal();
    setShowMission(true);
  };

  const onBackPress = () => {
    if (showMission) {
      setQuitModal(true);
    } else {
      goBack();
    }
  };

  // const {
  //   chatRef,
  //   messages,
  //   handleRecordAudio,
  //   fetchFirstMessage,
  //   handleBackChatBot,
  // } = useChatBot();
  // const {changeSource} = useSound();

  // useLayoutEffect(() => {
  //   fetchFirstMessage().then();
  // }, []);

  // const renderMessage = (props: any) => {
  //   const {currentMessage, position} = props;
  //   const isUser = position === 'right';

  //   const handleSpeakWork = () => {
  //     changeSource(currentMessage.audio);
  //   };

  //   return (
  //     <View
  //       style={[
  //         styles.messageContainer,
  //         isUser ? styles.messageUser : styles.messageBot,
  //       ]}>
  //       {!isUser && (
  //         <Image
  //           source={{
  //             uri: 'https://cdn-icons-png.flaticon.com/512/4712/4712139.png',
  //           }}
  //           style={styles.avatar}
  //           resizeMode="cover"
  //         />
  //       )}
  //       <Animated.View
  //         style={[
  //           styles.messageBubble,
  //           isUser ? styles.userBubble : styles.botBubble,
  //         ]}>
  //         <TypingText
  //           text={[currentMessage.text]}
  //           speed={10}
  //           style={{fontSize: 16, color: isUser ? '#fff' : '#000'}}
  //         />
  //         {!isUser && (
  //           <TouchableOpacity
  //             onPress={handleSpeakWork}
  //             hitSlop={HIT_SLOP}
  //             style={{marginTop: 15}}>
  //             <Image
  //               source={Theme.icons.speaker}
  //               style={{width: 20, height: 20}}
  //               resizeMode="cover"
  //             />
  //           </TouchableOpacity>
  //         )}
  //         <Title
  //           text={moment(currentMessage.createdAt).format('HH:mm')}
  //           style={[styles.timeText, {color: isUser ? '#eee' : '#888'}]}
  //         />
  //       </Animated.View>
  //       {isUser && (
  //         <Image
  //           source={{
  //             uri: 'https://cdn-icons-png.flaticon.com/512/847/847969.png',
  //           }}
  //           style={styles.avatar}
  //           resizeMode="cover"
  //         />
  //       )}
  //     </View>
  //   );
  // };

  // const renderInputToolBar = () => {
  //   return (
  //     <View style={styles.inputToolbarWrapper}>
  //       <RecordButtonWithRipple callBackRecording={handleRecordAudio} />
  //     </View>
  //   );
  // };
  {
    /*
          <GiftedChat
            messageContainerRef={chatRef}
            messages={messages}
            user={{_id: 1}}
            renderInputToolbar={renderInputToolBar}
            placeholder="Type your message here..."
            showUserAvatar={false}
            alwaysShowSend
            renderMessage={renderMessage}
            inverted={false}
            scrollToBottomOffset={100}
            infiniteScroll
            renderDay={() => <></>}
          /> */
  }

  const contentModal = () => {
    return (
      <View style={styles.outerContainer}>
        <TouchableOpacity style={styles.closeButton} onPress={handleCloseModal}>
          <Text>X</Text>
        </TouchableOpacity>
        <View style={styles.innerContainer}>
          <Text style={styles.titleText}>{modalContent?.title}</Text>
          <Image
            source={modalContent?.image}
            style={styles.image}
            resizeMode="cover"
          />
          <Text style={styles.questionText}>{modalContent?.question}</Text>
          <Text style={styles.missionTextModal}>{modalContent?.mission}</Text>
          <View style={styles.rewardContainer}>
            <Text style={styles.rewardText}>{modalContent?.reward}</Text>
          </View>
          <Button
            title="Start Now"
            onPress={handleStartNow}
            style={styles.startButton}
          />
        </View>
      </View>
    );
  };

  const contentQuitModal = () => {
    return (
      <View
        style={{
          backgroundColor: '#fff',
          paddingHorizontal: 20,
          paddingVertical: 20,
          borderRadius: 6,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <View
          style={{
            position: 'absolute',
            top: -100,
          }}>
          <SvgIcons.Quit />
        </View>
        <TouchableOpacity style={styles.closeButton} onPress={closeQuitModal}>
          <Text>X</Text>
        </TouchableOpacity>
        <View style={{height: 100}} />
        <Text style={{fontSize: 20, fontWeight: '700'}}>You want to quit?</Text>
        <Text style={{fontSize: 15, fontWeight: '400', textAlign: 'center'}}>
          You will lost your process. {'\n'}If yes, tap the button below.
        </Text>
        <Button title="Quit" onPress={handleQuit} style={styles.startButton} />
      </View>
    );
  };

  const renderFinishModal = () => {
    return (
      <View
        style={{
          backgroundColor: '#fff',
          paddingHorizontal: 20,
          paddingVertical: 20,
          borderRadius: 6,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Text style={{fontSize: 20, fontWeight: '700', color: '#F99A3Dho'}}>
          MISSION COMPLETE!
        </Text>
      </View>
    );
  };

  return (
    <ImageBackground
      style={styles.container}
      source={Theme.images.bgFunTalk}
      resizeMode="stretch">
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <Header onBackPress={onBackPress} />

      <CustomModal
        visible={modalVisible}
        animationIn={'slideInDown'}
        animationOut={'slideOutUp'}
        animationOutTiming={1000}
        animationInTiming={1000}
        children={contentModal()}
      />
      <CustomModal
        visible={quitModal}
        animationIn={'fadeIn'}
        animationOut={'fadeOut'}
        animationOutTiming={1000}
        animationInTiming={1000}
        children={contentQuitModal()}
      />
      <CustomModal
        visible={finishModal}
        animationIn={'fadeIn'}
        animationOut={'fadeOut'}
        animationOutTiming={1000}
        animationInTiming={1000}
        children={renderFinishModal()}
      />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F2F2F2',
    flex: 1,
    paddingBottom: 10,
  },
  outerContainer: {
    backgroundColor: '#FFEECD',
    borderRadius: 19,
    padding: 9,
  },
  closeButton: {
    position: 'absolute',
    right: -10,
    top: -10,
    width: 25,
    height: 25,
    backgroundColor: '#D9D9D9',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerContainer: {
    backgroundColor: '#fff',
    borderWidth: 3,
    borderColor: '#F99A3D',
    borderRadius: 19,
    paddingHorizontal: 15,
    paddingVertical: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FF7E00',
  },
  image: {
    width: 144,
    height: 144,
  },
  questionText: {
    fontSize: 18,
    fontWeight: '700',
  },
  missionTextModal: {
    fontSize: 12,
    fontWeight: '400',
    fontStyle: 'italic',
    lineHeight: 22,
    marginVertical: 10,
    textAlign: 'center',
  },
  rewardContainer: {
    backgroundColor: '#D9D9D9',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 7,
    marginTop: 5,
  },
  rewardText: {
    fontSize: 12,
    fontWeight: '400',
    fontStyle: 'italic',
  },
  startButton: {
    backgroundColor: '#F99A3D',
    width: '80%',
    marginTop: 10,
  },
  cardContainer: {
    flexDirection: 'row',
    gap: scale(5),
    justifyContent: 'center',
    position: 'absolute',
    alignSelf: 'center',
    bottom: verticalScale(175),
  },
  cardButton: {
    backgroundColor: '#FFA500',
    width: scale(60),
    height: verticalScale(100),
    borderRadius: moderateScale(12),
    margin: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardButtonText: {
    fontSize: moderateScale(14),
    color: 'white',
    fontWeight: 'bold',
  },
  missionContainer: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: verticalScale(160),
  },
  missionTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    color: '#FFF',
    textAlign: 'center',
  },
  missionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(10),
  },
  missionStatusIncomplete: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(24),
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#FF7E00',
    marginRight: scale(10),
  },
  missionStatusComplete: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(24),
    backgroundColor: '#00FF00',
    marginRight: scale(10),
  },
  missionText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
  },
  bottomContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    bottom: verticalScale(30),
    left: 35,
    right: 35,
    height: 35,
  },
  bottomButton: {
    width: 56,
    height: 37,
    borderRadius: 7,
    borderWidth: 2,
    borderColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomButtonText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
  },
});

export default ChatBot;
