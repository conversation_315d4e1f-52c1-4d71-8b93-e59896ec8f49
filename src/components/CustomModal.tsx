import React from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import Modal, {ModalProps} from 'react-native-modal';

interface CustomModalProps extends Partial<ModalProps> {
  visible: boolean;
  onClose?: () => void;
  children?: React.ReactNode;
  modalStyle?: ViewStyle;
  containerStyle?: ViewStyle;
}

const CustomModal: React.FC<CustomModalProps> = ({
  visible,
  onClose,
  children,
  backdropOpacity = 0.5,
  modalStyle,
  containerStyle,
  ...modalProps
}) => {
  return (
    <Modal
      isVisible={visible}
      backdropOpacity={backdropOpacity}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      useNativeDriver
      hideModalContentWhileAnimating
      {...modalProps}>
      <View style={[styles.modalContainer, modalStyle]}>
        <View style={containerStyle}>{children}</View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CustomModal;
