import React from 'react';
import {
  Animated,
  ColorValue,
  Platform,
  Pressable,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import { SvgIcons } from '../../../assets/svg/index.tsx';
import { useOtpInput } from '../../hooks/unAuth/useOtpInput';
import { useTheme } from '../../hooks/useTheme.ts';
import { useTranslate } from '../../hooks/useTranslate';
import { HIT_SLOP } from '../../utils/Scale';
import Button from '../Button.tsx';
import TextApp from '../TextApp';

interface VerticalStickProps {
  focusColor?: ColorValue;
  style?: ViewStyle;
  focusStickBlinkingDuration?: number;
}

export interface OtpInputRef {
  updateErorr: (error: string) => void;
  resendOTP: () => void;
}

interface OtpInputProps {
  loading: boolean;
  handleSubmitOTP: (otp: string) => void;
  handleResendOTP: () => void;
}

export const VerticalStick: React.FC<VerticalStickProps> = React.memo(
  ({focusColor, style, focusStickBlinkingDuration = 350}) => {
    const opacityAnim = React.useRef(new Animated.Value(1)).current;

    React.useEffect(() => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(opacityAnim, {
            toValue: 0,
            useNativeDriver: true,
            duration: focusStickBlinkingDuration,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            useNativeDriver: true,
            duration: focusStickBlinkingDuration,
          }),
        ]),
        {
          iterations: -1,
        },
      ).start();
    }, []);

    return (
      <Animated.View style={{opacity: opacityAnim}}>
        <View
          style={[
            styles.stick,
            focusColor ? {backgroundColor: focusColor} : {},
            style,
          ]}
          testID="otp-input-stick"
        />
      </Animated.View>
    );
  },
);

const numberOfDigits = 6;
const secureTextEntry = false;

export const OtpInput = React.forwardRef<OtpInputRef, OtpInputProps>(
  (props, ref) => {
    const {loading, handleSubmitOTP, handleResendOTP} = props;
    const {
      otp,
      timer,
      inputRef,
      focusedInputIndex,
      isFocused,
      handleTextChange,
      handlePress,
      handleFocus,
      handleBlur,
      isFullOTP,
      error,
      updateErorr,
      resendOTP,
    } = useOtpInput();
    const theme = useTheme();
    const {t} = useTranslate();

    React.useImperativeHandle(ref, () => ({
      updateErorr,
      resendOTP,
    }));

    return (
      <>
        <View style={[styles.container]}>
          {Array(numberOfDigits)
            .fill(0)
            .map((_, index) => {
              const char = otp[index] || '';
              const isFocusedInput =
                index === focusedInputIndex && Boolean(isFocused);
              const isFilledLastInput =
                otp.length === numberOfDigits && index === otp.length - 1;
              const isFocusedContainer =
                isFocusedInput || (isFilledLastInput && Boolean(isFocused));

              return (
                <Pressable
                  key={`${char}-${index}`}
                  onPress={handlePress}
                  style={[
                    styles.codeContainer,
                    isFocusedContainer
                      ? {borderColor: theme.border_brand}
                      : {
                          borderColor: error
                            ? theme.border_error
                            : char
                              ? theme.border_brand
                              : theme.border_secondary,
                        },
                  ]}>
                  {isFocusedInput ? (
                    <VerticalStick focusColor={theme.border_brand} />
                  ) : (
                    <TextApp
                      preset="text_xl_bold"
                      text={char && secureTextEntry ? '•' : char}
                      textColor={theme.text_primary}
                    />
                  )}
                </Pressable>
              );
            })}
          <TextInput
            value={otp}
            onChangeText={handleTextChange}
            maxLength={numberOfDigits}
            inputMode="numeric"
            textContentType="oneTimeCode"
            ref={inputRef}
            autoFocus
            secureTextEntry={secureTextEntry}
            autoComplete={
              Platform.OS === 'android' ? 'sms-otp' : 'one-time-code'
            }
            editable
            onFocus={handleFocus}
            onBlur={handleBlur}
            caretHidden={Platform.OS === 'ios'}
            style={styles.hiddenInput}
          />
        </View>
        <View style={styles.rowBox}>
          <TextApp
            preset="text_sm_regular"
            text={t('forgot.otp.resend_text')}
            textColor={theme.text_tertiary}
          />
          <TouchableOpacity
            onPress={handleResendOTP}
            hitSlop={HIT_SLOP}
            disabled={timer !== 0}>
            <TextApp
              preset="text_sm_semibold"
              text={t('forgot.otp.resend_action')}
              textColor={
                timer !== 0 ? theme.fg_disabled : theme.text_brand_tertiary
              }
              style={{marginHorizontal: scale(4)}}
            />
          </TouchableOpacity>
          {timer !== 0 && (
            <>
              <TextApp
                preset="text_sm_regular"
                text={`(${t('forgot.otp.resend_timer')} `}
                textColor={theme.text_tertiary}
              />
              <TextApp
                preset="text_sm_regular"
                text={`${timer}s`}
                textColor={theme.text_success_primary}
              />
              <TextApp
                preset="text_sm_regular"
                text={')'}
                textColor={theme.text_tertiary}
              />
            </>
          )}
        </View>
        {error && (
          <View style={styles.error}>
            <SvgIcons.Error />
            <TextApp
              text={error}
              preset="text_sm_regular"
              textColor={theme.text_error_primary}
              style={{marginLeft: scale(4)}}
            />
          </View>
        )}
        <Button
          title={t('forgot.otp.submit')}
          onPress={handleSubmitOTP?.bind(null, otp)}
          disabled={!isFullOTP}
          style={[
            styles.btn,
            {
              backgroundColor: !isFullOTP
                ? theme.bg_disabled
                : theme.bg_brand_solid,
              borderColor: !isFullOTP
                ? theme.border_disabled_subtle
                : theme.bg_brand_solid,
            },
          ]}
          textColor={!isFullOTP ? theme.fg_disabled : theme.text_white}
          loading={loading}
        />
      </>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
  },
  codeContainer: {
    borderWidth: 2,
    borderRadius: 10,
    borderColor: '#DFDFDE',
    height: scale(48),
    width: moderateVerticalScale(48),
    justifyContent: 'center',
    alignItems: 'center',
  },
  hiddenInput: {
    ...StyleSheet.absoluteFillObject,
    ...Platform.select({
      ios: {
        opacity: 0.02,
        color: 'transparent',
      },
      default: {
        opacity: 0,
      },
    }),
  },
  error: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: scale(34),
  },
  stick: {
    width: 2,
    height: 30,
    backgroundColor: '#539943',
  },
  rowBox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(12),
    marginHorizontal: scale(5),
  },
  btn: {
    marginTop: verticalScale(32),
    height: moderateVerticalScale(48),
  },
});
