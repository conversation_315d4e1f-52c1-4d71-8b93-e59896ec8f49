import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, TouchableWithoutFeedback, View} from 'react-native';
import Animated, {
  cancelAnimation,
  Easing,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {moderateVerticalScale, scale, verticalScale} from 'react-native-size-matters';

import {isAndroid, isIOS, SCREEN_WIDTH, widthScreen} from '../../utils/Scale';
import {Images} from '../../themes/images';
import TextApp from '../TextApp';
import FastImage from 'react-native-fast-image';
import {getImages} from '../../utils/getImages.ts';
import ProgressMission from '../ProgressMission.tsx';
import {Icons} from '../../themes/icons.ts';

const SIZE = 680;

interface PopupModalProps {
  isVisible: boolean;
  onClose: () => void;
  onStart: () => void;
  title: string;
  numberQuestion: number;
  numOfPass: number;
  isClock?: boolean;
  image: string;
}

const InfoUnitModal: React.FC<PopupModalProps> = ({
                                                    isVisible,
                                                    onClose,
                                                    title,
                                                    isClock,
                                                    numberQuestion,
                                                    numOfPass,
                                                    onStart,
                                                    image,
                                                  }) => {
  const rotation = useSharedValue(0);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    if (!isVisible) {
      return;
    }

    let isMounted = true;

    const loop = () => {
      if (!isMounted) {
        return;
      }
      rotation.value = withTiming(
        rotation.value + 360,
        {
          duration: 15000,
          easing: Easing.linear,
        },
        finished => {
          if (finished && isMounted) {
            runOnJS(loop)();
          }
        },
      );
    };

    loop();

    return () => {
      isMounted = false;
      cancelAnimation(rotation);
      setImageLoaded(false);
    };
  }, [rotation, isVisible]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{rotate: `${rotation.value % 360}deg`}],
  }));

  if (!isVisible) {
    return null;
  }

  return (
    <View style={styles.overlay}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalContent}>
          <Animated.View style={[styles.imageContainer, animatedStyle]}>

            <Image
              source={require('../../../assets/images/bgLight.webp')}
              style={styles.image}
              resizeMode="contain"
            />

          </Animated.View>

          <View style={styles.absoluteCenter}>
            <Image
              source={Images.bgPopupMission}
              resizeMode="contain"
              onLoad={() => setImageLoaded(true)}
              style={styles.popupImage}
            />
            <View style={styles.textContainer}>
              <View style={styles.contentContainer}>
                <FastImage
                  source={getImages(image, true)}
                  style={styles.unitImage}
                  resizeMode={'cover'}
                />
                <TextApp
                  text={title}
                  preset="text_lg_semibold"
                  textColor="#000000"
                  style={styles.titleText}
                />
                <View style={styles.progressContainer}>
                  <ProgressMission progress={numOfPass / numberQuestion} />
                  <View style={styles.progressRow}>
                    <View style={styles.questIconContainer}>
                      <Image
                        source={Icons.iconQuest}
                        resizeMode={'contain'}
                        style={styles.questIcon}
                      />
                      <TextApp
                        text="Quest"
                        preset="text_sm_medium"
                        style={styles.questText}
                      />
                    </View>
                    <TextApp
                      text={
                        numberQuestion ? `${numOfPass}/${numberQuestion}` : '0'
                      }
                      textColor="#535862"
                      preset="text_sm_medium"
                      style={styles.countText}
                    />
                  </View>
                </View>
              </View>
            </View>
          </View>

          <View style={styles.buttonRow}>
            {isClock ? (
              <ActionButton
                onPress={onClose}
                source={Images.btnLocked}
                widthVal={255}
              />
            ) : (
              <ActionButton
                onPress={onStart}
                source={Images.btnPopupMission}
                widthVal={255}
              />
            )}
          </View>
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const ActionButton = React.memo(
  ({
     onPress,
     source,
     widthVal,
   }: {
    onPress: () => void;
    source: any;
    widthVal: number;
  }) => (
    <TouchableOpacity onPress={onPress}>
      <Image
        source={source}
        resizeMode="contain"
        style={[styles.button, {width: scale(widthVal)}]}
      />
    </TouchableOpacity>
  ),
);

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 99,
    backgroundColor: 'rgba(10, 13, 18, 0.4)',
  },
  modalContent: {
    width: SCREEN_WIDTH,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    width: SIZE,
    height: SIZE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: SIZE,
    height: SIZE,
  },
  absoluteCenter: {
    width: widthScreen,
    position: 'absolute',
    alignItems: 'center',
  },
  popupImage: {
    width: scale(350),
    height: verticalScale(394),
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    position: 'absolute',
    alignItems: 'center',
    marginTop: moderateVerticalScale(110),
    width: scale(190),
    height: verticalScale(242),
    paddingTop: 50,
  },
  contentContainer: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    marginTop: isAndroid ? -20 : 0,
  },
  unitImage: {
    width: 120,
    height: 91,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#3A2514',
  },
  titleText: {
    textAlign: 'center',
    marginTop: 6,
    width: '100%',
    paddingHorizontal: 12,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    height: '30%',
    justifyContent: 'flex-end',
    marginTop: isIOS ? 4 : 0,
  },
  progressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '70%',
    marginTop: 12,
  },
  questIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  questIcon: {
    width: 16,
    height: 16,
  },
  questText: {
    marginLeft: 4,
  },
  countText: {
    textAlign: 'right',
  },
  buttonRow: {
    alignItems: 'center',
    marginTop: -50,
    justifyContent: 'center',
    width: widthScreen,
    paddingHorizontal: 16,
  },
  button: {
    height: verticalScale(96),
  },
});

export default InfoUnitModal;
