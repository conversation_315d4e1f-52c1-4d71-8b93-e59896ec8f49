import React from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import {Theme} from '../../themes';

import {
  moderateScale,
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg/index.tsx';
import {useTheme} from '../../hooks/useTheme.ts';

import Spacer from '../Spacer.tsx';
import TextApp from '../TextApp';
import {isIOS, SCREEN_WIDTH} from '../../utils/Scale.ts';

interface WellDoneProps {
  onPress: () => void;
  answer: string;
  onPreview: () => void;
}

const WellDone: React.FC<WellDoneProps> = ({onPress, answer, onPreview}) => {
  const theme = useTheme();
  return (
    <View style={[styles.container, {backgroundColor: theme.bg_secondary}]}>
      <Image
        source={Theme.images.completed}
        style={{
          width: scale(336),
          height: verticalScale(336),
          position: 'absolute',
          top: moderateVerticalScale(30),
        }}
        resizeMode={'contain'}
      />
      <TextApp
        text={'Completed!'}
        preset="display_xs_semibold"
        textColor={theme.text_brand_tertiary}
        style={{paddingTop: moderateVerticalScale(SCREEN_WIDTH / 1.8)}}
      />
      <Spacer size={10} />
      <TextApp
        text={'That was awesome! Ready for more?'}
        preset="text_md_regular"
        textColor={theme.text_secondary}
        style={{textAlign: 'center', lineHeight: 30}}
      />
      <Spacer size={12} />
      <View
        style={[
          styles.containerScore,
          {
            backgroundColor: theme.bg_brand_primary,
            borderColor: theme.border_brand,
          },
        ]}>
        <View style={styles.block}>
          <TextApp
            preset="text_md_regular"
            text={'Correct answer'}
            style={styles.label}
          />
          <TextApp preset="display_xs_semibold" text={answer} />
        </View>
        <View style={styles.separator} />
        <View style={styles.block}>
          <TextApp
            preset="text_md_regular"
            text={'Earned'}
            style={styles.label}
          />
          <TextApp preset="display_xs_semibold" text={'200 EXP'} />
        </View>
      </View>
      <Spacer size={24} />
      <TextApp
        text={'You got'}
        preset="display_xs_semibold"
        textColor={theme.text_brand_tertiary}
      />
      <Spacer size={8} />
      <View
        style={[styles.pearlBox, {backgroundColor: theme.bg_brand_primary}]}>
        <SvgIcons.IconPearl />
        <TextApp
          text={'50'}
          preset="display_xs_semibold"
          textColor={theme.text_primary}
          style={{marginLeft: scale(5)}}
        />
      </View>
      <View style={[styles.footer, {backgroundColor: theme.bg_primary}]}>
        <TouchableOpacity
          onPress={onPreview}
          style={[
            styles.detailButton,
            {
              backgroundColor: theme.bg_primary,
              borderColor: theme.border_primary,
            },
          ]}>
          <TextApp
            preset="text_md_semibold"
            text={'Review'}
            textColor={theme.text_secondary}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.continueButton,
            {
              backgroundColor: theme.bg_brand_solid,
            },
          ]}
          onPress={onPress}>
          <TextApp
            preset="text_md_semibold"
            text={'Continue'}
            textColor={theme.text_white}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  containerScore: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: Theme.radius.radius_2xl,
    overflow: 'hidden',
    marginHorizontal: scale(27.5),
  },
  block: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  separator: {
    width: 1,
    backgroundColor: '#F39C12',
  },
  label: {
    marginBottom: 4,
  },
  footer: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
    paddingHorizontal: moderateScale(32),
    paddingBottom: isIOS
      ? moderateVerticalScale(32)
      : moderateVerticalScale(16),
    paddingTop: moderateVerticalScale(16),
    gap: 10,
  },
  pearlBox: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Theme.spacing.spacing_md,
    paddingHorizontal: Theme.spacing.spacing_xl,
    borderRadius: Theme.radius.radius_full,
    shadowColor: '#0A0D121A',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 10,
  },
  detailButton: {
    flex: 1,
    height: 48,
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  continueButton: {
    flex: 1,
    height: 48,
    borderRadius: Theme.radius.radius_md,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default WellDone;
