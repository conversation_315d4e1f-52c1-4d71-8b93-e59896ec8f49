import React, {useEffect, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import <PERSON><PERSON><PERSON><PERSON><PERSON>iew from 'lottie-react-native';
import TextTranslator from '../sound/TextTranslator.tsx';
import {Theme} from '../../themes';
import {isAndroid, SCREEN_WIDTH} from '../../utils/Scale.ts';
import {useTypedSelector} from '../../redux/store.ts';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import {moderateScale, moderateVerticalScale, scale} from 'react-native-size-matters';
import {getImages} from '../../utils/getImages.ts';
import {FontFamily} from '../../themes/typography.ts';
import TextApp from '../TextApp';
import {useTheme} from '../../hooks/useTheme.ts';
import FastImage from 'react-native-fast-image';
import LoadingDots from '../LoadingDots.tsx';
import IconMic from '../../../assets/svgIcons/IconMic.tsx';
import ButtonFooter from '../ButtonFooter.tsx';

interface PronunciationTemplateProps {
  title?: string;
  question: string;
  answer?: string | boolean | number;
  audio: string;
  id: string;
  index: string | number;
  answerByStudent?: any;
  isDone?: boolean;
  image: string;
  exerciseType: ExerciseType;
}

const PronunciationTemplate: React.FC<PronunciationTemplateProps> = ({
                                                                       title,
                                                                       question,
                                                                       audio,
                                                                       id,
                                                                       index,
                                                                       answerByStudent,
                                                                       image,
                                                                       exerciseType,
                                                                     }) => {
  const data: any = useTypedSelector(state => state.question.dataPhonemes);
  const loading = useTypedSelector(state => state.question.loading);
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const [submit, setSubmit] = useState(false);
  const {handleToggleRecording, recording, clearPhonemes, handleCheckAnswer} = useQuestion();
  const theme = useTheme();
  const numWords = data?.word_scores?.length;
  const numCorrect = data?.word_scores.filter(
    (w: any) => w.score_grade === 'A' || w.score_grade === 'B',
  ).length;
  const isPass = numCorrect === numWords || numCorrect >= Math.ceil(numWords * 0.5);
  useEffect(() => {
    clearPhonemes();
  }, [id]);

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isDone
            ? moderateVerticalScale(isAndroid ? 130 : 150)
            : moderateVerticalScale(130),
        },
      ]}>
      <TextApp
        preset="text_md_semibold"
        textColor={theme.text_secondary}
        style={{
          textAlign: 'center',
          marginTop: isDone ? moderateVerticalScale(10) : 0,
        }}
        text={title}
      />

      <View style={styles.textContainer}>
        <TextTranslator
          disable={recording}
          text={question}
          audio={audio}
          scores={isDone ? answerByStudent?.word_scores : data?.word_scores}
        />
      </View>

      <View style={styles.audioContainer}>
        <View style={styles.shadowWrapper}>
          <View style={styles.boxMedia}>
            <FastImage
              resizeMode={'cover'}
              source={getImages(image, true)}
              defaultSource={Theme.images.boyAvt}
              style={styles.image}
            />
          </View>
        </View>

        {!isDone && !submit && (
          <TouchableOpacity
            activeOpacity={1}
            disabled={loading}
            style={[
              styles.audioWrapper,
              {backgroundColor: theme.bg_brand_solid},
            ]}
            onPress={() =>
              handleToggleRecording(question)
            }>
            {recording ? (
              <AnimatedLottieView
                source={require('../../../assets/lotties/record.json')}
                style={styles.lottie}
                autoPlay
                speed={0.7}
              />
            ) : loading ? (
              <LoadingDots />
            ) : (
              <IconMic />
            )}
          </TouchableOpacity>
        )}
        {!isDone && !submit && (
          <TextApp
            preset="text_md_semibold"
            textColor={theme.text_quaternary}
            style={{textAlign: 'center', marginTop: 20}}
            text={recording ? 'Tap to stop' : 'Tap to speak'}
          />
        )}
      </View>
      {!isDone && data?.word_scores && !recording && (
        <ButtonFooter
          title="Submit"
          btnCheck={() => {
            handleCheckAnswer(data, isPass, id, index, exerciseType, undefined, question);
            setSubmit(true);
          }
          }
        />
      )}
    </View>
  );
};

export default PronunciationTemplate;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: SCREEN_WIDTH,
    backgroundColor: '#fff',
  },
  title: {
    fontFamily: FontFamily.bold,
    fontSize: 16,
    textAlign: 'center',
    // marginBottom: moderateVerticalScale(60),
  },
  textContainer: {
    marginHorizontal: moderateScale(24),
    marginTop: moderateVerticalScale(8),
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flex: {
    flex: 1,
    marginLeft: 10,
  },
  audioContainer: {
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
  },
  audioWrapper: {
    width: scale(72),
    height: scale(72),

    borderRadius: Theme.radius.radius_full,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  audio: {
    width: 50,
    height: 50,
  },

  shadowWrapper: {
    shadowColor: '#0A0D121A',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 5,
  },
  boxMedia: {
    width: scale(300),
    height: scale(200),
    backgroundColor: '#FFFFFF',
    borderRadius: Theme.radius.radius_2xl,
    marginBottom: moderateVerticalScale(25),
    marginTop: moderateVerticalScale(20),
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  lottie: {
    width: scale(72),
    height: scale(72),
  },
});
