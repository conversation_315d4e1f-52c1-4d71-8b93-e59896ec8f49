import React from 'react';
import {StyleSheet, View} from 'react-native';
import {Theme} from '../themes';
import TextApp from './TextApp';
import {SvgIcons} from '../../assets/svg';
import {scale} from 'react-native-size-matters';
import {BaseToastProps, ToastConfig} from 'react-native-toast-message';
import {useTranslate} from '../hooks/useTranslate';

export const toastConfig: ToastConfig = {
  customError: props => <CustomErrorToast {...props} />,
  customSuccess: props => <CustomSuccessToast {...props} />,
};

const CustomErrorToast = ({text1}: BaseToastProps) => {
  const {t} = useTranslate();
  return (
    <View style={styles.toastContainer}>
      <SvgIcons.Warning />
      <View style={{flex: 1}}>
        <TextApp
          preset="text_md_regular"
          text={text1}
          style={{marginLeft: scale(10), lineHeight: 20}}
          textColor={'#D32F2F'}
        />
      </View>
    </View>
  );
};

const CustomSuccessToast = ({text1}: BaseToastProps) => {
  const {t} = useTranslate();
  return (
    <View style={[styles.toastContainer, {backgroundColor: '#D4F4D4'}]}>
      <View
        style={{
          width: 20,
          height: 20,
          backgroundColor: '#2E7D32',
          borderRadius: 20,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <SvgIcons.Check width={15} height={15} stroke={'#fff'} />
      </View>
      <View style={{flex: 1}}>
        <TextApp
          preset="text_md_regular"
          text={text1}
          style={{marginLeft: scale(10), lineHeight: 20}}
          textColor={'#2E7D32'}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    backgroundColor: '#FFE0E0',
    width: '90%',
    paddingHorizontal: Theme.spacing.spacing_2xl,
    paddingVertical: Theme.spacing.spacing_xl,
    borderRadius: Theme.radius.radius_md,
    shadowColor: '#0A0D121A',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 10,
    elevation: 5,
    flexDirection: 'row',
  },
});
