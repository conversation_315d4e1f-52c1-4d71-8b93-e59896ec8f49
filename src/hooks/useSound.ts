import {useEffect, useState} from 'react';
import SoundPlayer from 'react-native-sound-player';
import {API_INTEGRATE_URL} from '@env';
import HapticFeedback from 'react-native-haptic-feedback';

const useSound = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState<number | null>(null);
  const [currentUri, setCurrentUri] = useState<string | null>(null);

  const changeSource = (uri: string) => {
    const fullUri = API_INTEGRATE_URL + 'files/get-by-path?path=' + uri;

    stop();
    setCurrentUri(fullUri);
    setDuration(null);

    try {
      SoundPlayer.playUrl(fullUri);
      setIsPlaying(true);
    } catch (e) {
      console.log('Không thể play URL:', e);
      setIsPlaying(false);
    }
  };

  const play = () => {
    try {
      SoundPlayer.play();
      setIsPlaying(true);
    } catch (e) {
      console.log('play() lỗi:', e);
    }
  };

  const pause = () => {
    try {
      SoundPlayer.pause();
      setIsPlaying(false);
    } catch (e) {
      console.log('pause() lỗi:', e);
    }
  };

  const stop = () => {
    try {
      SoundPlayer.stop();
      setIsPlaying(false);
    } catch (e) {
      console.log('stop() lỗi:', e);
    }
  };

  const playLocalFile = (filename: string, activeHaptic: boolean) => {
    try {
      if (activeHaptic) {
        HapticFeedback.trigger('notificationSuccess', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });
      }

      SoundPlayer.playSoundFile(filename.replace(/\.mp3$/, ''), 'mp3');

      setIsPlaying(true);
    } catch (e) {
      console.log('playLocalFile() lỗi:', e);
      setIsPlaying(false);
    }
  };

  useEffect(() => {
    const finishedLoadingSub = SoundPlayer.addEventListener(
      'FinishedLoadingURL',
      async () => {
        try {
          const info = await SoundPlayer.getInfo();
          setDuration(info.duration);
        } catch (err) {
          console.log('getInfo() lỗi:', err);
        }
      },
    );

    const finishedPlayingSub = SoundPlayer.addEventListener(
      'FinishedPlaying',
      () => {
        setIsPlaying(false);
      },
    );

    return () => {
      finishedLoadingSub.remove();
      finishedPlayingSub.remove();
    };
  }, []);

  return {
    isPlaying,
    duration,
    changeSource,
    play,
    pause,
    stop,
    playLocalFile,
  };
};

export default useSound;
