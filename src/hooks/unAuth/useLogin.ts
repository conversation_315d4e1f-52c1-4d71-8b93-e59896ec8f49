import appleAuth from '@invertase/react-native-apple-authentication';
import {authorize} from 'react-native-app-auth';
import {useCallback, useEffect, useState} from 'react';
import {navigate, resetAndNavigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import {logIn, rememberPass} from '../../redux/reducer/AuthSlice';
import {fetchAuth, fetchDataInfo} from '../../redux/reducer/fetchData';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {isNullOrEmpty} from '../../utils';
import NotificationService from '../../../common/firebase/NotificationService';

const config = {
  issuer: 'https://idp.teca.vn/realms/elp',
  clientId: 'elp-web',
  redirectUrl: 'elp.teca.vn://callback',
  scopes: ['openid'],
  clientSecret: 'iZZHPh6CXPfczeL5VFp1I8rorm5jAQJw',
  additionalParameters: {
    kc_idp_hint: 'google',
    prompt: 'login' as 'login',
  },
  serviceConfiguration: {
    authorizationEndpoint:
      'https://idp.teca.vn/realms/elp/protocol/openid-connect/auth',
    tokenEndpoint:
      'https://idp.teca.vn/realms/elp/protocol/openid-connect/token',
    revocationEndpoint:
      'https://idp.teca.vn/realms/elp/protocol/openid-connect/logout',
  },
};

type FormDataType = {
  username: string;
  password: string;
};
const defaultFormData: FormDataType = {
  username: '',
  password: '',
};

export const useLogin = () => {
  const dispatch = useReduxDispatch();
  const remember = useTypedSelector(state => state.auth.remember);
  const loading = useTypedSelector(state => state.auth.loading);
  const [formData, setFormData] = useState<FormDataType>(defaultFormData);
  const [focused, setFocused] = useState({
    username: false,
    password: false,
  });
  const [isCheckBox, setIsCheckbox] = useState<boolean>(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (remember?.password) {
      setFormData(remember);
      setIsCheckbox(true);
    }
  }, [remember]);

  const handleRememberPass = () => {
    if (isNullOrEmpty(formData?.password)) return;
    setIsCheckbox(prev => {
      const nextState = !prev;
      dispatch(rememberPass(nextState ? formData : undefined));
      return nextState;
    });
  };

  const onChangeTextInput = useCallback(
    (key: keyof typeof formData, value: string) => {
      setFormData(prev => ({...prev, [key]: value}));
    },
    [],
  );

  const onFocusInput = (key: keyof typeof focused) => {
    setFocused(prev => ({...prev, [key]: true}));
  };

  const onBlurInput = (key: keyof typeof focused) => {
    setFocused(prev => ({...prev, [key]: false}));

    const value = formData[key]?.trim();

    if (!value) {
      const message =
        key === 'username'
          ? 'login.pleaseEnterUsername'
          : 'login.pleaseEnterPassword';
      setErrors(prev => ({...prev, [key]: message}));
      return;
    }
    setErrors(prev => ({...prev, [key]: ''}));
  };

  const handleForgotPassword = useCallback(() => {
    navigate(APP_SCREEN.FORGOT_PASSWORD);
  }, []);

  const handleRegister = useCallback(() => {
    navigate(APP_SCREEN.REGISTER);
  }, []);

  const handleSubmit = async () => {
    const params = {
      grant_type: 'password',
      client_id: 'elp-web',
      ...formData,
    };
    const result = await dispatch(fetchAuth(params));

    NotificationService.checkPendingNavigation();

    if (!fetchAuth.fulfilled.match(result)) return;

    const userResult = await dispatch(fetchDataInfo());

    if (!fetchDataInfo.fulfilled.match(userResult)) return;

    const {authorities} = userResult.payload;

    const nextScreen =
      !authorities || authorities.length === 0
        ? APP_SCREEN.CHOOSE_CHARACTER
        : APP_SCREEN.UN_AUTH;

    resetAndNavigate(nextScreen);
  };

  const handleLoginWithGoogle = async () => {
    try {
      const result = await authorize(config);
      if (!result) return;
      dispatch(logIn(result?.accessToken));
      NotificationService.checkPendingNavigation();
      const userResult = await dispatch(fetchDataInfo());
      if (!fetchDataInfo.fulfilled.match(userResult)) return;
      const {authorities} = userResult?.payload;
      if (!authorities || authorities.length === 0) {
        resetAndNavigate(APP_SCREEN.CHOOSE_CHARACTER);
      } else {
        resetAndNavigate(APP_SCREEN.UN_AUTH);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleLoginWithApple = async () => {
    try {
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });
      const credentialState = await appleAuth.getCredentialStateForUser(
        appleAuthRequestResponse.user,
      );
      if (
        credentialState === appleAuth.State.AUTHORIZED &&
        appleAuthRequestResponse.identityToken
      ) {
        console.log(appleAuthRequestResponse);
      }
    } catch (error) {
      console.log(error);
    }
  };

  return {
    formData,
    focused,
    errors,
    isCheckBox,
    loading,
    onChangeTextInput,
    onFocusInput,
    onBlurInput,
    handleSubmit,
    handleRegister,
    handleForgotPassword,
    handleRememberPass,
    handleLoginWithGoogle,
    handleLoginWithApple,
  };
};
