import {useCallback, useEffect} from 'react';
import Tts from 'react-native-tts';
import {isIOS} from '../utils/Scale';

export const useTTS = () => {
  useEffect(() => {
    Tts.setDefaultLanguage('en-GB');
    Tts.addEventListener('tts-start', () => {});
    Tts.addEventListener('tts-progress', () => {});
    Tts.addEventListener('tts-finish', () => {});

    return () => {
      Tts.removeAllListeners('tts-start');
      Tts.removeAllListeners('tts-progress');
      Tts.removeAllListeners('tts-finish');
    };
  }, []);
  const speakWord = useCallback((word: string) => {
    if (!word) {
      return;
    }
    Tts.speak(word, {
      iosVoiceId: isIOS ? 'com.apple.voice.compact.en-GB.Daniel' : '',
      rate: 0.3,
      androidParams: {
        KEY_PARAM_PAN: 0,
        KEY_PARAM_VOLUME: 1.0,
        KEY_PARAM_STREAM: 'STREAM_MUSIC',
      },
    });
  }, []);
  const stopSpeak = () => {
    Tts.stop();
  };
  return {
    speakWord,
    stopSpeak,
  };
};
