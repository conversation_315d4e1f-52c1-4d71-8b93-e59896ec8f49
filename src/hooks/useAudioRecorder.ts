import {useCallback, useEffect, useReducer, useRef} from 'react';
import AudioRecorderPlayer, {
  AudioEncoderAndroidType,
  AudioSourceAndroidType,
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
} from 'react-native-audio-recorder-player';
import {Platform} from 'react-native';
import {PERMISSIONS, requestMultiple, RESULTS} from 'react-native-permissions';
import {isAndroid} from '../utils/Scale.ts';

const audioRecorderPlayer = new AudioRecorderPlayer();

type State = {
  recording: boolean;
  playing: boolean;
  audioPath: string | null;
  recordTime: string;
  playTime: string;
  duration: string;
};

const initialState: State = {
  recording: false,
  playing: false,
  audioPath: null,
  recordTime: '00:00',
  playTime: '00:00',
  duration: '00:00',
};

const reducer = (state: State, action: Partial<State>): State => ({
  ...state,
  ...action,
});

const useAudioRecorder = () => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const recordSubscription = useRef<any>(null);
  const playSubscription = useRef<any>(null);

  const requestPermissions = async () => {
    const permissions = Platform.select({
      android: [PERMISSIONS.ANDROID.RECORD_AUDIO],
      ios: [PERMISSIONS.IOS.MICROPHONE],
    });

    const statuses = await requestMultiple(permissions || []);

    if (Platform.OS === 'android') {
      return statuses[PERMISSIONS.ANDROID.RECORD_AUDIO] === RESULTS.GRANTED;
    }

    return statuses[PERMISSIONS.IOS.MICROPHONE] === RESULTS.GRANTED;
  };

  const startRecording = useCallback(async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      console.warn('Không có quyền ghi âm!');
      return;
    }

    const path = Platform.select({
      ios: 'recording.m4a',
      android: undefined,
    });

    const audioSet = {
      AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
      AudioSourceAndroid: AudioSourceAndroidType.MIC,
      AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
      AVNumberOfChannelsKeyIOS: 2,
      AVFormatIDKeyIOS: AVEncodingOption.aac,
    };

    try {
      dispatch({recording: true});

      const uri = await audioRecorderPlayer.startRecorder(path, audioSet);
      if (!uri) {
        throw new Error('Không thể khởi động ghi âm!');
      }

      dispatch({audioPath: uri});

      recordSubscription.current = audioRecorderPlayer.addRecordBackListener(
        e => {
          dispatch({
            recordTime: audioRecorderPlayer.mmss(
              Math.floor(e.currentPosition / 1000),
            ),
          });
        },
      );
    } catch (error) {
      console.error('Lỗi khi ghi âm:', error);
      dispatch({recording: false});
    }
  }, [requestPermissions]);

  const stopRecording = useCallback(async () => {
    try {
      const result = await audioRecorderPlayer.stopRecorder();
      dispatch({recording: false, audioPath: result});
      recordSubscription.current?.remove();
      recordSubscription.current = null;
    } catch (error) {}
  }, []);

  const startPlaying = useCallback(async () => {
    if (!state.audioPath) return;

    try {
      dispatch({playing: true});
      await audioRecorderPlayer.startPlayer(state.audioPath);

      playSubscription.current = audioRecorderPlayer.addPlayBackListener(e => {
        dispatch({
          playTime: audioRecorderPlayer.mmss(
            Math.floor(e.currentPosition / 1000),
          ),
          duration: audioRecorderPlayer.mmss(Math.floor(e.duration / 1000)),
        });

        if (e.currentPosition >= e.duration) {
          stopPlaying();
        }
      });
    } catch (error) {}
  }, [state.audioPath]);

  const stopPlaying = useCallback(async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      dispatch({playing: false});
      playSubscription.current?.remove();
      playSubscription.current = null;
    } catch (error) {
      console.error('Lỗi khi dừng phát lại:', error);
    }
  }, []);

  useEffect(() => {
    return () => {
      recordSubscription.current?.remove();
      playSubscription.current?.remove();
    };
  }, []);

  return {
    ...state,
    startRecording,
    stopRecording,
    startPlaying,
    stopPlaying,
  };
};

export default useAudioRecorder;
