import {useCallback, useEffect, useRef, useState} from 'react';
import {GiftedChat, IMessage} from 'react-native-gifted-chat';
import {useTTS} from '../useTTS';
import {
  fetchChatbot,
  fetchChatbotPhonemes,
} from '../../redux/reducer/fetchData';
import useAudioRecorder from '../useAudioRecorder';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {resetDataChatbot} from '../../redux/reducer/ChatBotSlice';
import {goBack} from '../../navigation/NavigationServices';
import {isAndroid} from '../../utils/Scale';

export const useChatBot = () => {
  const {speakWord, stopSpeak} = useTTS();
  const chatRef = useRef<any>(null);
  const dispatch = useReduxDispatch();

  const {dataChatBot, dataLocal, loading} = useTypedSelector(
    state => state.chatbot,
  );

  const [messages, setMessages] = useState<IMessage[]>([]);
  const prevLengthRef = useRef(0);

  const {recording, audioPath, startRecording, stopRecording} =
    useAudioRecorder();

  const scrollToEnd = useCallback(() => {
    chatRef.current?.scrollToEnd?.({animated: true});
  }, []);

  useEffect(() => {
    const newItems = dataLocal.slice(prevLengthRef.current);

    if (newItems.length === 0) return;

    const newMessages = newItems.map(item => ({
      _id: `${Date.now()}-${Math.random()}`,
      text: item.message,
      createdAt: new Date(item.createdAt || Date.now()),
      user: {
        _id: item.sender === 'bot' ? 2 : 1,
        name: item.sender === 'bot' ? 'Bot' : 'You',
      },
    }));

    setMessages(prev => GiftedChat.append(prev, newMessages, false));
    speakWord(newMessages[newMessages.length - 1].text);
    scrollToEnd();

    prevLengthRef.current = dataLocal.length;
  }, [dataLocal, speakWord, scrollToEnd]);

  const fetchFirstMessage = useCallback(async () => {
    await dispatch(
      fetchChatbot({
        sessionId: '',
        message: 'Why do you want to learn English?',
      }),
    );
  }, [dispatch]);

  const handleRecordAudio = useCallback(async () => {
    if (!recording) {
      await startRecording();
      return;
    }

    await stopRecording();

    if (!audioPath) {
      console.warn('Không có file để upload!');
      return;
    }

    const formData = new FormData();
    const fileName = `audio_${Date.now()}.${isAndroid ? 'mp4' : 'm4a'}`;

    formData.append('audio', {
      uri: audioPath,
      name: fileName,
      type: isAndroid ? 'audio/mp4' : 'audio/m4a',
    });

    formData.append('sessionId', dataChatBot?.sessionId || '');

    const response = await dispatch(fetchChatbotPhonemes(formData));
    const newMessage = response?.payload?.data?.message;

    if (newMessage) {
      dispatch(
        fetchChatbot({
          sessionId: dataChatBot?.sessionId || '',
          message: newMessage,
        }),
      );
    }
  }, [
    recording,
    audioPath,
    dataChatBot?.sessionId,
    dispatch,
    startRecording,
    stopRecording,
  ]);

  const handleBackChatBot = useCallback(() => {
    goBack();
    dispatch(resetDataChatbot());
  }, [dispatch]);

  return {
    speakWord,
    chatRef,
    messages,
    recording,
    handleRecordAudio,
    fetchFirstMessage,
    handleBackChatBot,
    loadingChatBot: loading,
  };
};
