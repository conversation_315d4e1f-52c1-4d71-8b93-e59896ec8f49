// convert-svg-to-tsx.js
const fs = require('fs');
const path = require('path');

const svgFilePath = './class-leaderboard.svg'; // Đường dẫn file SVG gốc
const outputFilePath = './Charactor.tsx'; // File TSX đầu ra
const componentName = 'Charactor';

// Danh sách thẻ SVG cần viết hoa cho JSX
const jsxTags = [
  'svg',
  'path',
  'g',
  'defs',
  'clipPath',
  'rect',
  'circle',
  'polygon',
  'polyline',
  'line',
  'ellipse',
];

function capitalizeTag(tag) {
  return tag.replace(/<\/?(\w+)/g, (match, p1) => {
    const tagName = p1;
    const capitalized = tagName.charAt(0).toUpperCase() + tagName.slice(1);
    return match.replace(tagName, capitalized);
  });
}

function convertToTSX(svgContent) {
  const viewBoxMatch = svgContent.match(/viewBox="([^"]+)"/);
  const viewBox = viewBoxMatch ? viewBoxMatch[1] : '0 0 100 100';

  // Xoá thẻ <svg> và </svg>, giữ lại nội dung bên trong
  const innerSvg = svgContent
    .replace(/<svg[^>]*>/, '')
    .replace(/<\/svg>/, '')
    .trim();

  // Chuyển các thẻ svg thành PascalCase như Path, G, ClipPath,...
  let jsxContent = capitalizeTag(innerSvg);

  return `import React from 'react';
import Svg, { ${jsxTags.map(t => t.charAt(0).toUpperCase() + t.slice(1)).join(', ')} } from 'react-native-svg';

const ${componentName} = (props: any) => (
  <Svg
    width={props.width || 200}
    height={props.height || 200}
    viewBox="${viewBox}"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    ${jsxContent}
  </Svg>
);

export default ${componentName};
`;
}

function main() {
  if (!fs.existsSync(svgFilePath)) {
    console.error('❌ SVG file not found:', svgFilePath);
    return;
  }

  const svgContent = fs.readFileSync(svgFilePath, 'utf8');
  const tsxComponent = convertToTSX(svgContent);

  fs.writeFileSync(outputFilePath, tsxComponent, 'utf8');
  console.log(`✅ Converted ${svgFilePath} to ${outputFilePath}`);
}

main();
