/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';
import notifee from '@notifee/react-native';
import NotificationService from './common/firebase/NotificationService';

messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Background message received:', remoteMessage);
  // Don't display notification here - FCM automatically shows notification
  // when app is in background. We only handle data processing here.

  // Only display notification if it's a data-only message (no notification payload)
  if (!remoteMessage.notification && remoteMessage.data) {
    await NotificationService.displayNotification(remoteMessage);
  }
});

notifee.onBackgroundEvent(async ({type, detail}) => {
  console.log('Background Notifee event:', type, detail);
  // Handle notification tap events in background
  await NotificationService.handleBackgroundEvent({type, detail});
});

AppRegistry.registerComponent(appName, () => App);
