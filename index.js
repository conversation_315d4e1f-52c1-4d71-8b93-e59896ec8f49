/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';
import notifee, {EventType} from '@notifee/react-native';

messaging().setBackgroundMessageHandler(async remoteMessage => {
  if (remoteMessage?.notification) {
    await notifee.displayNotification({
      title: remoteMessage?.notification?.title,
      body: remoteMessage?.notification?.body,
      android: {
        channelId: 'default',
      },
    });
  }
});

notifee.onBackgroundEvent(async ({type, detail}) => {
  console.log('Background Notifee event:', type, detail);
});

AppRegistry.registerComponent(appName, () => App);
