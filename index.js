/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';
import notifee from '@notifee/react-native';
import NotificationService from './common/firebase/NotificationService';

messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Background message received:', remoteMessage);
  // Use NotificationService to handle background notifications
  await NotificationService.displayNotification(remoteMessage);
});

notifee.onBackgroundEvent(async ({type, detail}) => {
  console.log('Background Notifee event:', type, detail);
  // Handle notification tap events in background
  await NotificationService.handleBackgroundEvent({type, detail});
});

AppRegistry.registerComponent(appName, () => App);
