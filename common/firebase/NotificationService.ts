import messaging from '@react-native-firebase/messaging';
import notifee, {
  AndroidImportance,
  AuthorizationStatus,
  EventType,
} from '@notifee/react-native';
import { AppState } from 'react-native';
import { APP_SCREEN } from '../../src/navigation/screenType';
import { navigate } from '../../src/navigation/NavigationServices';
import store from '../../src/redux/store';

// Singleton class to manage notifications
class NotificationService {
  private static instance: NotificationService;
  private pendingNavigation: { screen: string; params?: any } | null = null;
  private lastNotificationId: string | null = null;
  private appState: string = AppState.currentState;

  private constructor() {
    // Private constructor to enforce singleton pattern
    // Listen for app state changes
    AppState.addEventListener('change', this.handleAppStateChange);
  }

  private handleAppStateChange = (nextAppState: string): void => {
    // When app comes to foreground from background
    if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
      console.log('App has come to the foreground!');
      // Check for pending navigation
      this.checkPendingNavigation();
    }
    this.appState = nextAppState;
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize notification channels and request permissions
   */
  public async initialize(): Promise<void> {
    await this.requestPermission();
    await this.createDefaultChannel();
    this.setupNotificationListeners();
    this.checkInitialNotification();
  }

  /**
   * Request notification permissions
   */
  private async requestPermission(): Promise<void> {
    const settings = await notifee.requestPermission();

    if (settings.authorizationStatus >= AuthorizationStatus.AUTHORIZED) {
      console.log('Notification permissions granted.');
    } else {
      console.warn('Notification permissions not granted.');
    }
  }

  /**
   * Create default notification channel (required for Android)
   */
  private async createDefaultChannel(): Promise<void> {
    await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
      importance: AndroidImportance.HIGH,
    });
  }

  /**
   * Get FCM device token for registration
   */
  public async getDeviceToken(): Promise<string | null> {
    try {
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  /**
   * Setup all notification listeners
   */
  private setupNotificationListeners(): void {
    // Foreground message handler - don't display notification when app is active
    messaging().onMessage(async (remoteMessage) => {
      console.log('Foreground message received:', remoteMessage);
      // Don't display notification when app is in foreground
      // User will see the notification content through the app's UI
    });

    // Background/Quit state notification tap handler
    messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('Notification opened app from background state:', remoteMessage);
      this.handleNotificationTap(remoteMessage);
    });

    // Foreground notification tap handler
    notifee.onForegroundEvent(({ type, detail }) => {
      if (type === EventType.PRESS) {
        console.log('User pressed notification in foreground:', detail);
        this.handleNotificationPress(detail);
      }
    });
  }

  /**
   * Check for initial notification that launched the app
   */
  private async checkInitialNotification(): Promise<void> {
    const remoteMessage = await messaging().getInitialNotification();
    
    if (remoteMessage) {
      console.log('App launched from notification:', remoteMessage);
      // Delay navigation to ensure app is fully initialized
      setTimeout(() => {
        this.handleNotificationTap(remoteMessage);
      }, 1000);
    }
  }

  /**
   * Display a notification
   */
  public async displayNotification(remoteMessage: any): Promise<void> {
    // Prevent duplicate notifications
    const messageId = remoteMessage.messageId || remoteMessage.notification?.title;
    if (messageId === this.lastNotificationId) {
      console.log('Preventing duplicate notification:', messageId);
      return;
    }
    
    this.lastNotificationId = messageId;

    await notifee.displayNotification({
      id: messageId,
      title: remoteMessage?.notification?.title,
      body: remoteMessage?.notification?.body,
      android: {
        channelId: 'default',
        pressAction: {
          id: 'open_notification',
        },
      },
      data: {
        target: APP_SCREEN.NOTIFICATION,
        ...remoteMessage.data,
      },
    });
  }

  /**
   * Handle notification tap from FCM
   */
  private handleNotificationTap(remoteMessage: any): void {
    const target = remoteMessage.data?.target || APP_SCREEN.NOTIFICATION;
    this.navigateFromNotification(target, remoteMessage.data);
  }

  /**
   * Handle notification press from Notifee
   */
  private handleNotificationPress(detail: any): void {
    const target = detail.notification?.data?.target || APP_SCREEN.NOTIFICATION;
    this.navigateFromNotification(target, detail.notification?.data);
  }

  /**
   * Navigate to target screen from notification
   */
  private navigateFromNotification(screen: string, data?: any): void {
    // Check if user is logged in
    const { token } = store.getState().auth;
    const { data: profileData } = store.getState().profile;

    const isLoggedIn = token && profileData && profileData.authorities && profileData.authorities.length > 0;

    if (!isLoggedIn) {
      // Save navigation for after login
      this.pendingNavigation = { screen, params: data };
      console.log('User not logged in, saving pending navigation:', this.pendingNavigation);
      return;
    }

    // User is logged in, navigate directly
    console.log('Navigating to:', screen, data);
    navigate(screen, data);
  }

  /**
   * Check for pending navigation after login
   */
  public checkPendingNavigation(): void {
    if (this.pendingNavigation) {
      console.log('Processing pending navigation:', this.pendingNavigation);

      // Double check if user is now logged in
      const { token } = store.getState().auth;
      const { data: profileData } = store.getState().profile;
      const isLoggedIn = token && profileData && profileData.authorities && profileData.authorities.length > 0;

      if (isLoggedIn) {
        navigate(this.pendingNavigation.screen, this.pendingNavigation.params);
        this.pendingNavigation = null;
      } else {
        console.log('User still not logged in, keeping pending navigation');
      }
    }
  }

  /**
   * Handle background notification events
   */
  public async handleBackgroundEvent({ type, detail }: { type: EventType, detail: any }): Promise<void> {
    if (type === EventType.PRESS) {
      console.log('User pressed notification in background:', detail);
      const target = detail.notification?.data?.target || APP_SCREEN.NOTIFICATION;
      // We can't navigate directly from background event handler
      // Instead, we'll store the target and handle it when app comes to foreground
      this.pendingNavigation = { screen: target, params: detail.notification?.data };
    }
  }
}

export default NotificationService.getInstance();
