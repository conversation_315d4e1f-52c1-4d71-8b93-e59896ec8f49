import messaging from '@react-native-firebase/messaging';
import notifee, {
  AndroidImportance,
  AuthorizationStatus,
  EventType,
} from '@notifee/react-native';
import {AppState} from 'react-native';
import {APP_SCREEN} from '../../src/navigation/screenType';
import {navigate} from '../../src/navigation/NavigationServices';
import store from '../../src/redux/store';

// State for the notification service
let pendingNavigation: {screen: string; params?: any} | null = null;
let lastNotificationId: string | null = null;
let appState: string = AppState.currentState;

// Setup app state change listener
AppState.addEventListener('change', (nextAppState: string) => {
  // When app comes to foreground from background
  if (appState.match(/inactive|background/) && nextAppState === 'active') {
    console.log('App has come to the foreground!');
    // Check for pending navigation
    NotificationService.checkPendingNavigation();
  }
  appState = nextAppState;
});

/**
 * Notification Service using functional approach with arrow functions
 */
const NotificationService = {
  /**
   * Initialize notification channels and request permissions
   */
  initialize: async (): Promise<void> => {
    await NotificationService.requestPermission();
    await NotificationService.createDefaultChannel();
    NotificationService.setupNotificationListeners();
    NotificationService.checkInitialNotification();
  },

  /**
   * Request notification permissions
   */
  requestPermission: async (): Promise<void> => {
    const settings = await notifee.requestPermission();

    if (settings.authorizationStatus >= AuthorizationStatus.AUTHORIZED) {
      console.log('Notification permissions granted.');
    } else {
      console.warn('Notification permissions not granted.');
    }
  },

  /**
   * Create default notification channel (required for Android)
   */
  createDefaultChannel: async (): Promise<void> => {
    await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
      importance: AndroidImportance.HIGH,
    });
  },

  /**
   * Get FCM device token for registration
   */
  getDeviceToken: async (): Promise<string | null> => {
    try {
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  },

  /**
   * Setup all notification listeners
   */
  setupNotificationListeners: (): void => {
    // Foreground message handler - only log, don't display notification
    messaging().onMessage(async remoteMessage => {
      console.log('Foreground message received:', remoteMessage);
      // Don't display notification when app is in foreground
      // Background handler will take care of displaying notifications
    });

    // Background/Quit state notification tap handler
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log(
        'Notification opened app from background state:',
        remoteMessage,
      );
      NotificationService.handleNotificationTap(remoteMessage);
    });

    // Foreground notification tap handler
    notifee.onForegroundEvent(({type, detail}) => {
      if (type === EventType.PRESS) {
        console.log('User pressed notification in foreground:', detail);
        NotificationService.handleNotificationPress(detail);
      }
    });
  },

  /**
   * Check for initial notification that launched the app
   */
  checkInitialNotification: async (): Promise<void> => {
    const remoteMessage = await messaging().getInitialNotification();

    if (remoteMessage) {
      console.log('App launched from notification:', remoteMessage);
      // Delay navigation to ensure app is fully initialized
      setTimeout(() => {
        NotificationService.handleNotificationTap(remoteMessage);
      }, 1000);
    }
  },

  /**
   * Display a notification
   */
  displayNotification: async (remoteMessage: any): Promise<void> => {
    // Create unique message ID for duplicate prevention
    const messageId =
      remoteMessage.messageId ||
      remoteMessage.notification?.title + '_' + Date.now();

    // Prevent duplicate notifications within 5 seconds
    if (messageId === lastNotificationId) {
      console.log('Preventing duplicate notification:', messageId);
      return;
    }

    lastNotificationId = messageId;

    // Clear lastNotificationId after 5 seconds to allow new notifications
    setTimeout(() => {
      if (lastNotificationId === messageId) {
        lastNotificationId = null;
      }
    }, 5000);

    console.log('Displaying notification:', messageId);

    await notifee.displayNotification({
      id: messageId,
      title: remoteMessage?.notification?.title || 'Thông báo',
      body: remoteMessage?.notification?.body || 'Bạn có thông báo mới',
      android: {
        channelId: 'default',
        pressAction: {
          id: 'open_notification',
        },
        // smallIcon: 'ic_notification',
        importance: AndroidImportance.HIGH,
      },
      data: {
        target: APP_SCREEN.NOTIFICATION,
        messageId: messageId,
        ...remoteMessage.data,
      },
    });
  },

  /**
   * Handle notification tap from FCM
   */
  handleNotificationTap: (remoteMessage: any): void => {
    console.log('Handling FCM notification tap:', remoteMessage);
    const target = remoteMessage.data?.target || APP_SCREEN.NOTIFICATION;
    const params = remoteMessage.data ? {...remoteMessage.data} : undefined;
    NotificationService.navigateFromNotification(target, params);
  },

  /**
   * Handle notification press from Notifee
   */
  handleNotificationPress: (detail: any): void => {
    console.log('Handling Notifee notification press:', detail);
    const target = detail.notification?.data?.target || APP_SCREEN.NOTIFICATION;
    const params = detail.notification?.data
      ? {...detail.notification.data}
      : undefined;
    NotificationService.navigateFromNotification(target, params);
  },

  /**
   * Navigate to target screen from notification
   */
  navigateFromNotification: (screen: string, data?: any): void => {
    // Add delay to ensure navigation is ready
    setTimeout(() => {
      // Check if user is logged in
      const {token} = store.getState().auth;
      const {data: profileData} = store.getState().profile;

      const isLoggedIn =
        token &&
        profileData &&
        profileData.authorities &&
        profileData.authorities.length > 0;

      if (!isLoggedIn) {
        // Save navigation for after login
        pendingNavigation = {screen, params: data};
        console.log(
          'User not logged in, saving pending navigation:',
          pendingNavigation,
        );
        return;
      }

      // User is logged in, navigate directly
      console.log('Navigating to:', screen, data);
      navigate(screen, data);
    }, 500);
  },

  /**
   * Check for pending navigation after login
   */
  checkPendingNavigation: (): void => {
    if (pendingNavigation) {
      console.log('Processing pending navigation:', pendingNavigation);

      // Double check if user is now logged in
      const {token} = store.getState().auth;
      const {data: profileData} = store.getState().profile;
      const isLoggedIn =
        token &&
        profileData &&
        profileData.authorities &&
        profileData.authorities.length > 0;

      if (isLoggedIn) {
        navigate(pendingNavigation.screen, pendingNavigation.params);
        pendingNavigation = null;
      } else {
        console.log('User still not logged in, keeping pending navigation');
      }
    }
  },

  /**
   * Handle background notification events
   */
  handleBackgroundEvent: async ({
    type,
    detail,
  }: {
    type: EventType;
    detail: any;
  }): Promise<void> => {
    if (type === EventType.PRESS) {
      console.log('User pressed notification in background:', detail);
      const target =
        detail.notification?.data?.target || APP_SCREEN.NOTIFICATION;
      const params = detail.notification?.data
        ? {...detail.notification.data}
        : undefined;

      // Store navigation to handle when app comes to foreground
      pendingNavigation = {screen: target, params};
      console.log(
        'Stored pending navigation from background:',
        pendingNavigation,
      );
    }
  },
};

export default NotificationService;
