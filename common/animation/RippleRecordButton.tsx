import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Animated, {
  cancelAnimation,
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import {Theme} from '../../src/themes';

const Ripple = ({
  delay = 0,
  isRecording,
}: {
  delay: number;
  isRecording: boolean;
}) => {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(1);

  useEffect(() => {
    if (isRecording) {
      scale.value = withDelay(
        delay,
        withRepeat(
          withTiming(2, {
            duration: 2000,
            easing: Easing.out(Easing.ease),
          }),
          -1,
          false,
        ),
      );
      opacity.value = withDelay(
        delay,
        withRepeat(
          withTiming(0, {
            duration: 2000,
            easing: Easing.out(Easing.ease),
          }),
          -1,
          false,
        ),
      );
    } else {
      cancelAnimation(scale);
      cancelAnimation(opacity);
      scale.value = 0;
      opacity.value = 1;
    }
  }, [isRecording]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scale: scale.value}],
    opacity: opacity.value,
  }));

  return <Animated.View style={[styles.ripple, animatedStyle]} />;
};

export const RecordButtonWithRipple = ({
  callBackRecording,
}: {
  callBackRecording?: () => void;
}) => {
  const [isRecording, setIsRecording] = useState<boolean>(false);

  const iconOpacity = useSharedValue(1);
  const iconScale = useSharedValue(1);
  const iconRotate = useSharedValue(0);

  const toggleRecording = () => {
    setIsRecording(prev => !prev);
    callBackRecording?.();
  };

  useEffect(() => {
    iconOpacity.value = 0;
    iconScale.value = 0.8;

    iconOpacity.value = withTiming(1, {duration: 300});
    iconScale.value = withTiming(1, {duration: 300});

    iconRotate.value = withTiming(isRecording ? 1 : 0, {duration: 300});
  }, [isRecording]);

  const animatedIconStyle = useAnimatedStyle(() => ({
    opacity: iconOpacity.value,
    transform: [
      {scale: iconScale.value},
      {rotate: `${iconRotate.value * 360}deg`},
    ],
  }));

  return (
    <View style={styles.container}>
      <Ripple delay={0} isRecording={isRecording} />
      <Ripple delay={500} isRecording={isRecording} />
      <Ripple delay={1000} isRecording={isRecording} />
      <TouchableOpacity style={styles.button} onPress={toggleRecording}>
        <Animated.Image
          source={isRecording ? Theme.icons.arrowTop : Theme.icons.audio}
          style={[styles.icon, animatedIconStyle]}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  ripple: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 100,
    backgroundColor: '#F99A3D',
  },
  button: {
    width: 71,
    height: 71,
    borderRadius: 100,
    backgroundColor: '#F99A3D',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#F99A3D',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
    borderWidth: 5,
    borderColor: '#fff',
  },
  icon: {
    width: 35,
    height: 35,
    tintColor: '#fff',
  },
});
